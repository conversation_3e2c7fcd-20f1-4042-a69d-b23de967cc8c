package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.service.TmsCustomerOrderService;
import com.jygjexp.jynx.tms.service.TmsOrderTrackNew;
import com.jygjexp.jynx.tms.vo.TmsWebOrderTrackVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: xiongpengfei
 * @Description: 中大件轨迹接口装载方法接口
 * @Date: 2025/8/19 10:07
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TmsOrderTrackNewImpl implements TmsOrderTrackNew {

    private final TmsCustomerOrderService customerOrderService;

    // 原zxoms官网查询接口兼容查询nb中大件
    @Override
    public void processNOrderNos(List<String> nOrderNos, String zipInput, JSONArray ja, Set<String> handledPkgNos) {
        try {
            // 构建TmsWebOrderTrackVo参数
            TmsWebOrderTrackVo vo = new TmsWebOrderTrackVo();
            vo.setOrderList(nOrderNos);
            vo.setZip(zipInput);

            // 调用TmsCustomerOrderService.getZdjWebTrackNew()方法
            R result = customerOrderService.getZdjWebTrackNew(vo);

            if (result.getCode() == 0 && result.getData() != null) {
                // 转换getZdjWebTrackNew的返回格式为getTracksFromZxoms的统一格式
                convertZdjWebTrackToUnifiedFormat(result.getData(), ja, handledPkgNos, nOrderNos);
                log.info("N开头单号轨迹查询成功，订单号：{}", nOrderNos);
            } else {
                log.warn("N开头单号轨迹查询失败，订单号：{}，错误信息：{}", nOrderNos, result.getMsg());
            }
        } catch (Exception e) {
            log.error("N开头单号轨迹查询异常，订单号：{}", nOrderNos, e);
        }
    }


    /**
     * 转换统一格式
     * @param data getZdjWebTrackNew返回的数据
     * @param ja 结果集合
     * @param handledPkgNos 已处理的包裹号集合
     * @param orderNos 订单号列表
     */
    private void convertZdjWebTrackToUnifiedFormat(Object data, JSONArray ja, Set<String> handledPkgNos, List<String> orderNos) {
        try {
            if (data instanceof Map) {
                Map<String, Object> response = (Map<String, Object>) data;
                Map<String, Object> result = (Map<String, Object>) response.get("result");

                if (result != null) {
                    for (Map.Entry<String, Object> entry : result.entrySet()) {
                        String mainOrderNo = entry.getKey();
                        Map<String, Object> mainOrderInfo = (Map<String, Object>) entry.getValue();
                        List<Map<String, Object>> subOrders = (List<Map<String, Object>>) mainOrderInfo.get("subOrders");

                        if (CollUtil.isNotEmpty(subOrders)) {
                            for (Map<String, Object> subOrder : subOrders) {
                                String subOrderNo = (String) subOrder.get("subOrderNo");

                                // 检查是否是请求的订单号之一
                                if (orderNos.contains(subOrderNo) || orderNos.contains(mainOrderNo)) {
                                    JSONObject orderJo = new JSONObject();
                                    orderJo.set("pkgNo", subOrderNo);
                                    orderJo.set("orderStatus", subOrder.get("orderStatus"));
                                    orderJo.set("destination", subOrder.get("destination"));

                                    // 转换轨迹格式
                                    List<Map<String, Object>> trackList = (List<Map<String, Object>>) subOrder.get("trackList");
                                    if (CollUtil.isNotEmpty(trackList)) {
                                        JSONArray trackJa = trackList.stream()
                                                .map(track -> {
                                                    JSONObject jo = new JSONObject();
                                                    jo.set("code", track.get("code"));
                                                    jo.set("time", track.get("trackTime"));
                                                    jo.set("status", "");
                                                    jo.set("city", track.get("city"));
                                                    jo.set("content", track.get("trackDesc"));
                                                    jo.set("timezone", track.get("timezone"));
                                                    return jo;
                                                }).collect(Collectors.toCollection(JSONArray::new));
                                        orderJo.set("track", trackJa);
                                    }

                                    // 处理签收图片
                                    String signImgUrl = (String) subOrder.get("signImgUrl");
                                    if (StrUtil.isNotBlank(signImgUrl)) {
                                        orderJo.set("images", Arrays.asList(signImgUrl));
                                    }

                                    ja.add(orderJo);
                                    handledPkgNos.add(subOrderNo);
                                    if (!subOrderNo.equals(mainOrderNo)) {
                                        handledPkgNos.add(mainOrderNo);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("转换getZdjWebTrackNew返回格式异常", e);
        }
    }




}
