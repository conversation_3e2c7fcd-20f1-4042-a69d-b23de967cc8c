package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.time.LocalDateTime;

/**
 * 服务商邮编分区配置表
 *
 * <AUTHOR>
 * @date 2025-07-10 10:34:38
 */
@Data
@TenantTable
@TableName("tms_service_region")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "服务商邮编分区配置表")
public class TmsServiceRegionEntity extends BaseLogicEntity<TmsServiceRegionEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long regionId;

	/**
	* 分区代码
	*/
    @Schema(description="分区代码")
    private String regionCode;

	/**
	* 分区名称
	*/
    @Schema(description="分区名称")
    private String regionName;

	/**
	* 分区类型，可达分区、不可达分区
	*/
    @Schema(description="分区类型，1 可达分区、 0 不可达分区")
    private Boolean regionType;

	/**
	* 启用状态：0：禁用、1：启用
	*/
    @Schema(description="启用状态：0：禁用、1：启用")
    private Integer isValid;

}