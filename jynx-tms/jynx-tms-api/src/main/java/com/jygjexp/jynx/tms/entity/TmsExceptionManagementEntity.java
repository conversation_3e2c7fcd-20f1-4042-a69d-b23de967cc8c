package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.time.LocalDateTime;

/**
 * TMS异常管理表
 *
 * <AUTHOR>
 * @date 2025-03-04 15:31:27
 */
@Data
@TenantTable
@TableName("tms_exception_management")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "TMS异常管理表")
public class TmsExceptionManagementEntity extends BaseLogicEntity<TmsExceptionManagementEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long exceptionId;

	/**
	* 异常单号
	*/
    @Schema(description="异常单号")
    private String exceptionOrderNo;

	/**
	* 异常状态(0:待处理, 1:已处理)
	*/
    @Schema(description="异常状态(0:待处理, 1:已处理)")
    private Integer exceptionStatus;

	/**
	* 调度单号
	*/
    @Schema(description="调度单号/委托单号")
    private String dispatchOrderNo;

	/**
	* 车牌号
	*/
    @Schema(description="车牌号")
    private String licensePlate;

	/**
	* 异常时间
	*/
    @Schema(description="异常时间")
    private LocalDateTime exceptionTime;

	/**
	* 异常类型
	*/
    @Schema(description="异常类型")
    private Integer exceptionType;

	/**
	* 异常地点
	*/
    @Schema(description="异常地点")
    private String exceptionLocation;

	/**
	* 异常描述
	*/
    @Schema(description="异常描述")
    private String exceptionDescription;

	/**
	 * 异常描述
	 */
	@Schema(description="异常来源")
	private Integer exceptionSource ;

	/**
	* 现场图片（最多六张，用逗号分隔）
	*/
    @Schema(description="现场图片（最多六张，用逗号分隔）")
    private String imageUrls;

	/**
	* 处理方案
	*/
    @Schema(description="处理方案")
    private Integer handlingPlan;

	/**
	* 处理说明
	*/
    @Schema(description="处理说明")
    private String handlingDescription;

	/**
	 * 上报司机
	 */
	@Schema(description="上报司机")
	private String driverName;

}