package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 司机计费模板-计件配置表
 *
 * <AUTHOR>
 * @date 2025-07-21 18:59:09
 */
@Data
@TableName("tms_driver_billing_piece")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "司机计费模板-计件配置表")
public class TmsDriverBillingPieceEntity extends Model<TmsDriverBillingPieceEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long pieceId;

	/**
	* 模板ID，关联 driver_billing_template.id
	*/
    @Schema(description="模板ID，关联 driver_billing_template.id")
    private Long templateId;

	/**
	* 出勤要求(天)
	*/
    @Schema(description="出勤要求(天)")
    private Integer attendanceRequirement;

	/**
	* 节假日自动减少天数
	*/
    @Schema(description="节假日自动减少天数")
    private Integer autoReduceOnHoliday;

	/**
	* 保底工资(CAD)
	*/
    @Schema(description="保底工资(CAD)")
    private BigDecimal minimumWage;

	/**
	* 面签工资(CAD)
	*/
    @Schema(description="面签工资(CAD)")
    private BigDecimal faceSignWage;

	/**
	* 奖励金(CAD)
	*/
    @Schema(description="奖励金(CAD)")
    private BigDecimal bonus;
 
	/**
	* createTime
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="createTime")
    private LocalDateTime createTime;
 
	/**
	* updateTime
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="updateTime")
    private LocalDateTime updateTime;
}