package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 车辆路线访问点
 *
 * <AUTHOR>
 * @date 2025-03-17 15:40:29
 */
@Data
@TableName("tms_vehicle_route_visit")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "车辆路线访问点")
public class TmsVehicleRouteVisitEntity extends Model<TmsVehicleRouteVisitEntity> {


	/**
	* 路线顺序访问点id
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="路线顺序访问点id")
    private Long vehicleRouteVisitId;

	/**
	* 车辆路线规划id
	*/
    @Schema(description="车辆路线规划id")
    private Long vehicleRouteId;

	/**
	* 访问顺序
	*/
	@Schema(description="访问顺序")
	private Integer orderNum;

	/**
	 * 派送任务索引值
	 */
	@Schema(description="派送任务索引值")
	private Integer shipmentIndex;

	/**
	* 是否是提货点（0：false，1：true）
	*/
    @Schema(description="是否是提货点（0：false，1：true）")
    private Integer isPickup;

	/**
	* 到达时间
	*/
    @Schema(description="到达时间")
    private LocalDateTime arrivalTime;

	/**
	* 绕道时间
	*/
    @Schema(description="绕道时间")
    private String detour;

	/**
	* 配送任务标签（订单id字符串）
	*/
    @Schema(description="配送任务标签（订单id字符串）")
    private String shipmentLabel;

	/**
	* 提货重量（派送重量）
	*/
    @Schema(description="提货重量（派送重量）")
    private BigDecimal loadWeight;
	/**
	 * 纬度值
	 */
	@Schema(description="纬度值")
	private BigDecimal latitude;
	/**
	 * 经度值
	 */
	@Schema(description="经度值")
	private BigDecimal longitude;
	/**
	 * 重新规划标志点（默认值0（只进行了一次路径规划：中途没有进行重新进行路径规划），重新规划加一，即表示进行了多少次重新规划，用于将此时最新的visit线路查询出来）
	 */
	@Schema(description="重新规划标志点")
	private Integer replanningSign;

	/**
	 * 到达该点的时间
	 */
	@Schema(description="到达该点的时间")
	private LocalDateTime actualArrivalTime;

	/**
	 * 状态
	 */
	@Schema(description="状态")
	private Integer status;

	/**
	 * 站点id
	 */
	@Schema(description="站点id")
	private Integer siteId;

	/**
	 * 乐观锁
	 */
	@Schema(description="乐观锁")
	private Integer revision;

	/**
	 * 备注
	 */
	@Schema(description="备注")
	private String remark;

	/**
	 * 创建人
	 */
	@TableField(fill = FieldFill.INSERT)
	@Schema(description="创建人")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	@Schema(description="创建时间")
	private LocalDateTime createTime;

	/**
	 * 更新人
	 */
	@TableField(fill = FieldFill.UPDATE)
	@Schema(description="更新人")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.UPDATE)
	@Schema(description="更新时间")
	private LocalDateTime updateTime;

	/**
	 * 逻辑删除
	 */
	@TableLogic
	@TableField(fill = FieldFill.INSERT)
	@Schema(description="逻辑删除")
	private String delFlag;

	/**
	 * 租户号
	 */
	@Schema(description="租户号")
	private Integer tenantId;
}