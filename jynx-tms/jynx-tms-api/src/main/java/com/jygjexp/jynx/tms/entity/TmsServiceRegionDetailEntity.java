package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 服务商邮编配置子表（分区明细）
 *
 * <AUTHOR>
 * @date 2025-07-10 14:49:36
 */
@Data
@TenantTable
@TableName("tms_service_region_detail")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "服务商邮编配置子表（分区明细）")
public class TmsServiceRegionDetailEntity extends BaseLogicEntity<TmsServiceRegionDetailEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 分区ID
	*/
    @Schema(description="分区ID")
    private Long regionId;

	/**
	 * 邮编分区
	 */
	@Schema(description="邮编分区")
	private String postalName;

	/**
	* 邮编
	*/
    @Schema(description="邮编")
    private String postalCode;

	/**
	 * 发件人起始邮编
	 */
	@Schema(description="发件人起始邮编")
	private String shipperPostalCodeStart;

	/**
	 * 发件人结束邮编
	 */
	@Schema(description="发件人结束邮编")
	private String shipperPostalCodeEnd;


	/**
	* 起始邮编(目的地)
	*/
    @Schema(description="(目的地)起始邮编")
    private String postalCodeStart;

	/**
	* 结束邮编(目的地)
	*/
    @Schema(description="(目的地)结束邮编")
    private String postalCodeEnd;

	/**
	* 时效（单位：天）
	*/
    @Schema(description="时效（单位：天）")
    private BigDecimal transitDays;

	/**
	* 落地口岸
	*/
    @Schema(description="落地口岸")
    private String portName;

	/**
	* 启用状态：0：禁用、1：启用
	*/
    @Schema(description="启用状态：0：禁用、1：启用")
    private Integer isValid;
}