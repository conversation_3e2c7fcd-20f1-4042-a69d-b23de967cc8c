package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.time.LocalDateTime;

/**
 * 派送失败图片记录
 *
 * <AUTHOR>
 * @date 2025-06-09 19:42:15
 */
@Data
@TenantTable
@TableName("tms_delivery_failed_proof")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "派送失败图片记录")
public class TmsDeliveryFailedProofEntity extends BaseLogicEntity<TmsDeliveryFailedProofEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 委托订单号
	*/
    @Schema(description="委托订单号")
    private String entrustedOrderNo;

	/**
	* 第几次派送失败
	*/
    @Schema(description="第几次派送失败")
    private Integer deliveryTry;

	/**
	* 失败证明图片地址
	*/
    @Schema(description="失败证明图片地址")
    private String proofImageUrl;


	/**
	 * 派送失败原因
	 */
	@Schema(description="派送失败原因")
	private Integer failedReason;



}