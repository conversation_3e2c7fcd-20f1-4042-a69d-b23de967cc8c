package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 车辆路线规划
 *
 * <AUTHOR>
 * @date 2025-03-17 15:36:10
 */
@Data
@TableName("tms_vehicle_route")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "车辆路线规划")
public class TmsVehicleRouteEntity extends Model<TmsVehicleRouteEntity> {


	/**
	* 车辆路线规划id
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="车辆路线规划id")
    private Long vehicleRouteId;

	/**
	* 路线规划主表id
	*/
    @Schema(description="路线规划主表id")
    private Long routePlanId;

	/**
	 * 运输单号
	 */
	@Schema(description="运输单号")
	private String shipmentNo;


	/**
	* 车辆标签（车辆唯一标识-司机id字符串）
	*/
    @Schema(description="车辆标签（车辆唯一标识-司机id字符串）")
    private String vehicleLabel;

	/**
	* 车辆开始时间
	*/
    @Schema(description="车辆开始时间")
    private LocalDateTime vehicleStartTime;

	/**
	* 车辆结束时间
	*/
    @Schema(description="车辆结束时间")
    private LocalDateTime vehicleEndTime;

	/**
	* 路线点字符串
	*/
    @Schema(description="路线点字符串")
    private String routePolylinePoints;

	/**
	 * 暂时路线点字符串（存储重新规划的线路的线路点串，没有重新规划为null值）
	 */
	@Schema(description="暂时路线点字符串（存储重新规划的线路的线路点串，没有重新规划为null值）")
	private String tempRoutePolylinePoints;

	/**
	* 路线规划成功的派送任务数量
	*/
    @Schema(description="路线规划成功的派送任务数量")
    private Integer performedShipmentCount;

	/**
	* 派送路程中持续时间，单位秒（s）
	*/
    @Schema(description="派送路程中持续时间，单位秒（s）")
    private String travelDuration;

	/**
	* 等待持续时间，单位秒（s）
	*/
    @Schema(description="等待持续时间，单位秒（s）")
    private String waitDuration;

	/**
	* 延误持续时间，单位秒（s）
	*/
    @Schema(description="延误持续时间，单位秒（s）")
    private String delayDuration;
 
	/**
	* breakDuration
	*/
    @Schema(description="breakDuration")
    private String breakDuration;

	/**
	* 派送持续时间（到达后派送持续）
	*/
    @Schema(description="派送持续时间（到达后派送持续）")
    private String visitDuration;

	/**
	* 线路总共持续时间
	*/
    @Schema(description="线路总共持续时间")
    private String totalDuration;

	/**
	* 线路总长
	*/
    @Schema(description="线路总长")
    private Long travelDistanceMeters;

	/**
	* 最大载重（本次路线的重量累加）-单位：克，防止谷歌地图转整数时丢失精度为0的情况
	*/
    @Schema(description="最大载重（本次路线的重量累加）")
    private BigDecimal totalLoad;

	/**
	* 路线费用项字符串
	*/
	@Schema(description="路线费用项字符串")
	private String routeCosts;

	/**
	* 总共成本花费
	*/
    @Schema(description="总共成本花费")
    private BigDecimal routeTotalCosts;

	/**
	 * 路线经纬度点字符串
	 */
	@Schema(description="路线经纬度点字符串")
	private String routeString;

	/**
	 * 重新规划标志点（默认值0（只进行了一次路径规划：中途没有进行重新进行路径规划），重新规划加一，即表示进行了多少次重新规划，用于将此时最新的visit线路查询出来）
	 */
	@Schema(description="重新规划标志点")
	private Integer replanningSign;
	/**
	 * 当前点纬度
	 */
	@Schema(description="当前点纬度")
	private BigDecimal currentPointLat;
	/**
	 * 当前点经度
	 */
	@Schema(description="当前点经度")
	private BigDecimal currentPointLng;


	/**
	 * 状态
	 */
	@Schema(description="状态")
	private Integer status;

	/**
	 * 站点id
	 */
	@Schema(description="站点id")
	private Integer siteId;

	/**
	 * 乐观锁
	 */
	@Schema(description="乐观锁")
	private Integer revision;

	/**
	 * 备注
	 */
	@Schema(description="备注")
	private String remark;

	/**
	 * 创建人
	 */
	@TableField(fill = FieldFill.INSERT)
	@Schema(description="创建人")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	@Schema(description="创建时间")
	private LocalDateTime createTime;

	/**
	 * 更新人
	 */
	@TableField(fill = FieldFill.UPDATE)
	@Schema(description="更新人")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.UPDATE)
	@Schema(description="更新时间")
	private LocalDateTime updateTime;

	/**
	 * 逻辑删除
	 */
	@TableLogic
	@TableField(fill = FieldFill.INSERT)
	@Schema(description="逻辑删除")
	private String delFlag;

	/**
	 * 租户号
	 */
	@Schema(description="租户号")
	private Integer tenantId;
	/**
	 * 批次号+路线编号串（查询使用）
	 */
	@Schema(description="批次号+路线编号串（查询使用）")
	private String batchRouteNumString;

	/**
	 *起点纬度
	 */
	@Schema(description = "起点纬度")
	private BigDecimal startLat;
	/**
	 *起点经度
	 */
	@Schema(description = "起点经度")
	private BigDecimal startLng;
	/**
	 *终点纬度
	 */
	@Schema(description = "终点纬度")
	private BigDecimal endLat;
	/**
	 *终点经度
	 */
	@Schema(description = "终点经度")
	private BigDecimal endLng;

}