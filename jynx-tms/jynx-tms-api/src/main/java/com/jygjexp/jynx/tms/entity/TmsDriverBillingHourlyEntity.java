package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 司机计费模板-时薪配置表
 *
 * <AUTHOR>
 * @date 2025-07-21 18:58:43
 */
@Data
@TableName("tms_driver_billing_hourly")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "司机计费模板-时薪配置表")
public class TmsDriverBillingHourlyEntity extends Model<TmsDriverBillingHourlyEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long hourId;

	/**
	* 模板ID，关联 driver_billing_template.id
	*/
    @Schema(description="模板ID，关联 driver_billing_template.id")
    private Long templateId;

	/**
	* 所在省份
	*/
    @Schema(description="所在省份")
    private String province;

	/**
	* 基本时薪(CAD)
	*/
    @Schema(description="基本时薪(CAD)")
    private BigDecimal baseHourlyWage;

	/**
	* 加班类型
	*/
    @Schema(description="加班类型")
    private Integer overtimeType;

	/**
	* 出勤要求(天)
	*/
    @Schema(description="出勤要求(天)")
    private Integer attendanceRequirement;

	/**
	* 每日保底(小时)
	*/
    @Schema(description="每日保底(小时)")
    private BigDecimal dailyMinimum;

	/**
	* 节假日自动减少天数
	*/
    @Schema(description="节假日自动减少天数")
    private Integer autoReduceOnHoliday;

	/**
	* 奖励金(CAD)
	*/
    @Schema(description="奖励金(CAD)")
    private BigDecimal bonus;

	/**
	* 公休资格计算符号（>,>=,= 等）
	*/
    @Schema(description="公休资格计算符号（>,>=,= 等）")
    private String restCalcOperator;

	/**
	* 公休工资倍数
	*/
    @Schema(description="公休工资倍数")
	private BigDecimal restSalaryMultiplier;

	/**
	 * 每日超8小时加班倍数
	 */
	@Schema(description="每日超8小时加班倍数")
	private BigDecimal overtimeEightHours;

	/**
	 * 每日超12小时加班倍数
	 */
	@Schema(description="每日超12小时加班倍数")
	private BigDecimal overtimeTwelveHours;

	/**
	 * 每周超40小时加班倍数
	 */
	@Schema(description="每周超40小时加班倍数")
	private BigDecimal overtimeFortyHours;

	/**
	 * 每周超44小时加班倍数
	 */
	@Schema(description="每周超44小时加班倍数")
	private BigDecimal overtimeFortyfourHours;

 
	/**
	* createTime
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="createTime")
    private LocalDateTime createTime;
 
	/**
	* updateTime
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="updateTime")
    private LocalDateTime updateTime;
}