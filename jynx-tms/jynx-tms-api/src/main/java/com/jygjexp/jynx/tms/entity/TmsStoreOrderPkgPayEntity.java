package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 快递订单包裹赔付表
 *
 * <AUTHOR>
 * @date 2025-07-21 11:48:01
 */
@Data
@TenantTable
@TableName("tms_store_order_pkg_pay")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "快递订单包裹赔付表")
public class TmsStoreOrderPkgPayEntity extends Model<TmsStoreOrderPkgPayEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 订单ID
	*/
    @Schema(description="订单ID")
    private Long orderId;

	/**
	* 赔付原因
	*/
    @Schema(description="赔付原因")
    private String compensationReason;

	/**
	* 赔付金额
	*/
    @Schema(description="赔付金额")
    private BigDecimal compensationAmount;

	/**
	* 赔付币种
	*/
    @Schema(description="赔付币种")
    private String currency;

	/**
	* 赔付凭证
	*/
    @Schema(description="赔付凭证")
    private String voucher;

	/**
	* 备注
	*/
    @Schema(description="备注")
    private String remark;

	/**
	* 乐观锁
	*/
    @Schema(description="乐观锁")
    private Long revision;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 更新人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人")
    private String updateBy;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

	/**
	* 删除标志（0-正常，1-已删除）
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标志（0-正常，1-已删除）")
    private String delFlag;

	/**
	* 租户号
	*/
    @Schema(description="租户号")
    private Long tenantId;
}