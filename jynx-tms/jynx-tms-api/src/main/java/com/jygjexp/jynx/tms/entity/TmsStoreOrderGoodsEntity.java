package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 门店订单货物信息
 *
 * <AUTHOR>
 * @date 2025-07-14 17:39:58
 */
@Data
@TenantTable
@TableName("tms_store_order_goods")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "门店订单货物信息")
public class TmsStoreOrderGoodsEntity extends Model<TmsStoreOrderGoodsEntity> {


    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键ID")
    private Long id;

    /**
     * 门店跟踪主单号
     */
    @Schema(description="门店跟踪主单号")
    private String mainEntrustedOrder;


    /**
     * 门店跟踪子单号
     */
    @Schema(description="门店跟踪子单号")
    private String subEntrustedOrder;


    /**
     * 单位制类型:0公制 1:英制
     */
    @Schema(description="单位制类型:0公制 1:英制")
    private Integer unitType;

    /**
     * 长
     */
    @Schema(description="长")
    private BigDecimal length;

    /**
     * 宽
     */
    @Schema(description="宽")
    private BigDecimal width;

    /**
     * 高
     */
    @Schema(description="高")
    private BigDecimal height;

    /**
     * 重量
     */
    @Schema(description="重量")
    private BigDecimal weight;

    /**
     * 货物价值
     */
    @Schema(description="货物价值")
    private BigDecimal goodValue;

    /**
     * 货物信息
     */
    @Schema(description="货物信息")
    private String goodInfo;

    /**
     * 乐观锁
     */
    @Schema(description="乐观锁")
    private Integer revision;

    /**
     * 备注
     */
    @Schema(description="备注")
    private String remark;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

    /**
     * 删除标记：0未删除，1已删除
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标记：0未删除，1已删除")
    private String delFlag;

    /**
     * 租户号
     */
    @Schema(description="租户号")
    private Long tenantId;
}
