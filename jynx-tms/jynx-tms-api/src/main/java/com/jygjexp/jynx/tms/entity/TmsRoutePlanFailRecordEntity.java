package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 路径规划失败记录表
 *
 * <AUTHOR>
 * @date 2025-03-24 11:43:06
 */
@Data
@TenantTable
@TableName("tms_route_plan_fail_record")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "路径规划失败记录表")
public class TmsRoutePlanFailRecordEntity extends Model<TmsRoutePlanFailRecordEntity> {


	/**
	* id
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="id")
    private Long id;

	/**
	* 路径规划失败key（司机id）
	*/
    @Schema(description="路径规划失败key（司机id）")
    private String failKey;

	/**
	* 失败订单的日期
	*/
    @Schema(description="失败订单的日期")
    private LocalDate date;

	/**
	* 失败订单
	*/
    @Schema(description="失败订单")
    private String failOrder;

	/**
	 * 失败原因
	 */
	@Schema(description = "失败原因")
	private String failReason;

	/**
	* 是否处理（0：未处理，1：处理）
	*/
    @Schema(description="是否处理（0：未处理，1：处理）")
    private Integer isHandle;

	/**
	 * 失败类型
	 */
	@Schema(description="失败类型")
	private String type;

	/**
	* 状态
	*/
    @Schema(description="状态")
    private Integer status;

	/**
	* 站点id
	*/
    @Schema(description="站点id")
    private Integer siteId;

	/**
	* 乐观锁
	*/
    @Schema(description="乐观锁")
    private Integer revision;

	/**
	* 备注
	*/
    @Schema(description="备注")
    private String remark;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 更新人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人")
    private String updateBy;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

	/**
	* 逻辑删除
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="逻辑删除")
    private String delFlag;

	/**
	* 租户号
	*/
    @Schema(description="租户号")
    private Integer tenantId;
}