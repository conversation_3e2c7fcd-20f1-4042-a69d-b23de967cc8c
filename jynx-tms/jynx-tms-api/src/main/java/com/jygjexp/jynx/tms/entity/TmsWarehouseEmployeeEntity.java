package com.jygjexp.jynx.tms.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.core.util.TenantTable;
import com.jygjexp.jynx.tms.annotation.ConvertType;
import com.jygjexp.jynx.tms.utils.IntegerDictsConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 仓库员工
 *
 * <AUTHOR>
 * @date 2024-10-30 21:24:45
 */
@Data
@TenantTable
@TableName("tms_warehouse_employee")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "仓库员工")
public class TmsWarehouseEmployeeEntity extends Model<TmsWarehouseEmployeeEntity> {


	/**
	* 员工ID
	*/
	@ExcelIgnore
    @TableId(type = IdType.AUTO)
    @Schema(description="员工ID")
    private Integer employeeId;


	/**
	 * 租户号
	 */
	@Schema(description="租户号")
	private Long tenantId;

	/**
	* 员工类型：1=管理员，2=配送员
	*/
    @Schema(description="员工类型：1=管理员，2=普通人员")
	@ExcelProperty(value = "(role)角色", converter = IntegerDictsConverter.class)
	@ConvertType("employeeType")
    private Integer employeeType;

	/**
	* 微信
	*/
    @Schema(description="微信")
	@ExcelIgnore
    private Integer weixinId;

	/**
	 * 登录密码
	 */
	@Schema(description="登录密码")
	@ExcelIgnore
	private String password;

	/**
	* 添加日期
	*/
    @Schema(description="添加日期")
	@ExcelProperty(value = "(Add date)添加日期")
	@ColumnWidth(25)
    private LocalDateTime createDate;

	/**
	* 是否有效
	*/
	@ExcelProperty(value = "(valid or not)是否有效", converter = IntegerDictsConverter.class)
	@ConvertType("boolean")
    private Integer isValid;

	/**
	* 驿站
	*/
    @Schema(description="驿站")
	@ExcelIgnore
    private Integer postId;

	/**
	* 真实姓名
	*/
    @Schema(description="真实姓名")
	@ExcelIgnore
    private String realname;

	/**
	* 联系电话
	*/
    @Schema(description="手机号")
	@ColumnWidth(25)
	@ExcelProperty(value = "(phone)手机号")
    private String mobile;

	/**
	* 用户ID
	*/
    @Schema(description="用户ID")
	@ExcelIgnore
    private Integer userId;

	/**
	* PDA功能
	*/
    @Schema(description="PDA功能")
	@ExcelIgnore
    private Integer supportPda;

	/**
	* 驿站名称
	*/
    @Schema(description="驿站名称")
	@ExcelIgnore
	@TableField(exist = false)
    private String postName;


}