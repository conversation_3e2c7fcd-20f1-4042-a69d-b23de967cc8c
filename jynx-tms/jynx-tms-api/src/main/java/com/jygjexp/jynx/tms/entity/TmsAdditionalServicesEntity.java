package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.time.LocalDateTime;

/**
 * 卡派-附加服务
 *
 * <AUTHOR>
 * @date 2025-03-07 16:18:31
 */
@Data
@TenantTable
@TableName("tms_additional_services")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "卡派-附加服务")
public class TmsAdditionalServicesEntity extends BaseLogicEntity<TmsAdditionalServicesEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	 * 客户单号
	 */
	@Schema(description="客户单号")
	private String customerOrderNumber;

	/**
	* 委托单号
	*/
    @Schema(description="委托单号")
    private String entrustedOrderNumber;

	/**
	* 附加服务类型
	*/
    @Schema(description="附加服务类型")
    private String additionalServiceType;

	/**
	* 附加服务取值
	*/
    @Schema(description="附加服务取值")
    private String additionalServiceValue;

	/**
	* 附加服务时间
	*/
    @Schema(description="附加服务时间")
    private String additionalServiceTime;

	/**
	* 是否城市/郊区：0 郊区，1 城市
	*/
    @Schema(description="是否城市/郊区：0 郊区，1 城市")
    private Integer isUrban;


}