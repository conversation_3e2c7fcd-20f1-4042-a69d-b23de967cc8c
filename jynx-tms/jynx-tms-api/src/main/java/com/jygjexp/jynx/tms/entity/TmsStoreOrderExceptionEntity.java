package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.time.LocalDateTime;

/**
 * 快递订单异常处理表
 *
 * <AUTHOR>
 * @date 2025-07-21 11:47:35
 */
@Data
@TenantTable
@TableName("tms_store_order_exception")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "快递订单异常处理表")
public class TmsStoreOrderExceptionEntity extends Model<TmsStoreOrderExceptionEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 订单ID，关联订单表
	*/
    @Schema(description="订单ID，关联订单表")
    private Long orderId;

	/**
	* 异常类型
	*/
    @Schema(description="异常类型")
    private Integer exceptionType;

	/**
	* 异常原因描述
	*/
    @Schema(description="异常原因描述")
    private String reason;

	/**
	* 乐观锁版本
	*/
    @Schema(description="乐观锁版本")
    private Long revision;

	/**
	* 备注
	*/
    @Schema(description="备注")
    private String remark;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 更新人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人")
    private String updateBy;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

	/**
	* 删除标志（0-正常，1-已删除）
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标志（0-正常，1-已删除）")
    private String delFlag;

	/**
	* 租户号
	*/
    @Schema(description="租户号")
    private Long tenantId;
}