package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.time.LocalDateTime;

/**
 * 中大件揽收派送报告
 *
 * <AUTHOR>
 * @date 2025-06-05 11:47:18
 */
@Data
@TenantTable
@TableName("tms_report_management")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "中大件揽收派送报告")
public class TmsReportManagementEntity extends BaseLogicEntity<TmsReportManagementEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 报告单号
	*/
    @Schema(description="报告单号")
    private String reportOrderNo;

	/**
	* 报告类型：1=揽收，2=派送
	*/
    @Schema(description="报告类型：1=揽收报告，2=派送报告")
    private Integer reportType;

	/**
	* 派单数量
	*/
    @Schema(description="派单数量")
    private Integer dispatchNum;

	/**
	* 已扫数量
	*/
    @Schema(description="已扫数量")
    private Integer scanedNum;

	/**
	* 未扫数量
	*/
    @Schema(description="未扫数量")
    private Integer unscanNum;

	/**
	 * 待退还数量
	 */
	@Schema(description="待退还数量")
	private Integer returnNum;

	/**
	 * 总共已退还数量
	 */
	@Schema(description="总共已退还数量")
	private Integer thenReturnNum;


}