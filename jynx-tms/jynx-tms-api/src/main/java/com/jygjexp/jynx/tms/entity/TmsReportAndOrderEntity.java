package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 报告-订单中间表
 *
 * <AUTHOR>
 * @date 2025-06-24 10:09:30
 */
@Data
@TableName("tms_report_and_order")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "报告-订单中间表")
public class TmsReportAndOrderEntity extends Model<TmsReportAndOrderEntity> {


	/**
	* 主键
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键")
    private Long id;

	/**
	* 报告单号
	*/
    @Schema(description="报告单号")
    private String reportOrderNo;

	/**
	* 跟踪单号
	*/
    @Schema(description="跟踪单号")
    private String orderNo;

	/**
	 * 批次号
	 */
	@Schema(description="批次号")
	private String batchNo;

	/**
	* 类型 1 已扫描 2 未扫描 3 待退还
	*/
    @Schema(description="类型 1 已扫描 2 未扫描 3 待退还")
    private Integer orderType;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;
}