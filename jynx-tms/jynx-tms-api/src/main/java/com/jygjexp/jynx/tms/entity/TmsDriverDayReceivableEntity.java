package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 司机每日应收费用表
 *
 * <AUTHOR>
 * @date 2025-07-22 17:00:53
 */
@Data
@TenantTable
@TableName("tms_driver_day_receivable")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "司机每日应收费用表")
public class TmsDriverDayReceivableEntity extends Model<TmsDriverDayReceivableEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 司机ID
	*/
    @Schema(description="司机ID")
    private Long driverId;

	/**
	* 车辆类型
	*/
    @Schema(description="车辆类型")
    private Integer vehicleType;

	/**
	* 出勤日期
	*/
    @Schema(description="出勤日期")
    private LocalDate attendanceDate;

	/**
	* 出勤小时数
	*/
    @Schema(description="出勤小时数")
    private BigDecimal attendanceHours;

	/**
	* 订单数量
	*/
    @Schema(description="订单数量")
    private Integer orderCount;

	/**
	* 奖励金
	*/
    @Schema(description="奖励金")
    private BigDecimal bonus;

	/**
	* 面签费用
	*/
    @Schema(description="面签费用")
    private BigDecimal faceSignFee;

	/**
	* 包裹运费
	*/
    @Schema(description="包裹运费")
    private BigDecimal freightFee;

	/**
	* 保底前费用
	*/
    @Schema(description="保底前费用")
    private BigDecimal preMinimumTotal;

	/**
	* 保底后费用
	*/
    @Schema(description="保底后费用")
    private BigDecimal postMinimumTotal;

	/**
	* 乐观锁
	*/
    @Schema(description="乐观锁")
    private Long revision;

	/**
	* 备注
	*/
    @Schema(description="备注")
    private String remark;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 更新人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人")
    private String updateBy;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

	/**
	* 删除标志
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标志")
    private String delFlag;

	/**
	* 租户号
	*/
    @Schema(description="租户号")
    private Long tenantId;
}