package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.time.LocalDateTime;

/**
 * 司机分配历史表
 *
 * <AUTHOR>
 * @date 2025-07-03 09:36:54
 */
@Data
@TenantTable
@TableName("tms_driver_assign_history")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "司机分配历史表")
public class TmsDriverAssignHistoryEntity extends Model<TmsDriverAssignHistoryEntity> {


	/**
	* 主键
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键")
    private Long id;

	/**
	* 订单号
	*/
    @Schema(description="订单号")
    private String orderNo;

	/**
	* 分配类型（1: 揽收, 2: 派送, 3: 干线）
	*/
    @Schema(description="分配类型（1: 揽收, 2: 派送, 3: 干线）")
    private Integer assignType;

	/**
	* 分配司机ID
	*/
    @Schema(description="分配司机ID")
    private Long driverId;

	/**
	* 分配司机名称
	*/
    @Schema(description="分配司机名称")
    private String driverName;

	/**
	 * 分配司机号
	 */
	@Schema(description="分配司机号")
	private String driverNum;

	/**
	 * 是否转单（0：否；1、是）
	 */
	@Schema(description="是否转单（0：否；1、是）")
	private Integer isTransfer;

	/**
	* 状态
	*/
    @Schema(description="状态")
    private Integer status;

	/**
	* 站点id
	*/
    @Schema(description="站点id")
    private Integer siteId;

	/**
	* 乐观锁
	*/
    @Schema(description="乐观锁")
    private Integer revision;

	/**
	* 备注
	*/
    @Schema(description="备注")
    private String remark;

	/**
	* 分配描述
	*/
    @Schema(description="分配描述")
    private String description;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 更新人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人")
    private String updateBy;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

	/**
	* 逻辑删除
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="逻辑删除")
    private String delFlag;

	/**
	* 租户号
	*/
    @Schema(description="租户号")
    private Integer tenantId;
}