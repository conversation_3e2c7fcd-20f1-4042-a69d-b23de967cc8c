package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.time.LocalDateTime;

/**
 * 仓位信息
 *
 * <AUTHOR>
 * @date 2025-06-06 14:36:15
 */
@Data
@TenantTable
@TableName("tms_warehouse_location")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "仓位信息")
public class TmsWarehouseLocationEntity extends BaseLogicEntity<TmsWarehouseLocationEntity> {


	/**
	* 仓位主键id
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="仓位主键id")
    private Long locationId;

	/**
	* 所属仓库
	*/
    @Schema(description="所属仓库")
    private Long warehouseId;

	/**
	* 仓位编码
	*/
    @Schema(description="仓位编码")
    private String locationCode;

	/**
	* 库存数量
	*/
    @Schema(description="库存数量")
    private Integer quantity;

	/**
	* 使用状态：0：被占用、1：未占用
	*/
    @Schema(description="使用状态：0：被占用、1：未占用")
    private Integer useValid;

	/**
	* 启用状态：0：禁用、1：启用
	*/
    @Schema(description="启用状态：0：禁用、1：启用")
    private Integer isValid;


}