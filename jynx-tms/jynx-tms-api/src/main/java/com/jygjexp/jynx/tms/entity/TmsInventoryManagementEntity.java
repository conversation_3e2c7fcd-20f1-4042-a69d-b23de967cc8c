package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 库存管理
 *
 * <AUTHOR>
 * @date 2025-04-06 21:46:02
 */
@Data
@TenantTable
@TableName("tms_inventory_management")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "库存管理")
public class TmsInventoryManagementEntity extends BaseLogicEntity<TmsInventoryManagementEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	 * 站点ID
	 */
	@Schema(description = "站点ID")
	private Long siteId;

	/**
	* 仓库名称
	*/
    @Schema(description="仓库名称")
    private String warehouseName;

	/**
	* 仓库类型：0：一级仓，1：二级仓，3：驿站
	*/
    @Schema(description="仓库类型：0：一级仓，1：二级仓，3：驿站")
    private Integer warehouseType;

	/**
	* 票数
	*/
    @Schema(description="票数")
    private Integer orderNum;

	/**
	* 货物数量
	*/
    @Schema(description="货物数量")
    private Integer cargoQuantity;

	/**
	* 总重量(kg)
	*/
    @Schema(description="总重量(kg)")
    private BigDecimal totalWeight;

	/**
	* 总体积(m³)
	*/
    @Schema(description="总体积(m³)")
    private BigDecimal totalVolume;

	/**
	 * 托盘数
	 */
	@Schema(description="托盘数")
	private Integer trayNum;

	/**
	 * 包裹数
	 */
	@Schema(description="包裹数")
	private Integer pkgNum;

}