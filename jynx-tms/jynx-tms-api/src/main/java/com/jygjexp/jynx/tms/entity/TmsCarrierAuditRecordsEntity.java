package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.time.LocalDateTime;

/**
 * 卡派-承运商审核记录
 *
 * <AUTHOR>
 * @date 2025-03-14 16:29:42
 */
@Data
@TenantTable
@TableName("tms_carrier_audit_records")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "卡派-承运商审核记录")
public class TmsCarrierAuditRecordsEntity extends BaseLogicEntity<TmsCarrierAuditRecordsEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 客户单号
	*/
    @Schema(description="客户单号")
    private String customerOrderNumber;

	/**
	 * 委托单号
	 */
	@Schema(description="委托单号")
	private String entrustedOrderNumber;

	/**
	* 承运商编码
	*/
    @Schema(description="承运商编码")
    private String carrierCode;

	/**
	* 承运商名称
	*/
    @Schema(description="承运商名称")
    private String carrierName;

	/**
	* 承运商类型。1：自营、2：外包
	*/
    @Schema(description="承运商类型。1：自营、2：外包")
    private Integer carrierType;

	/**
	* 联系人姓名
	*/
    @Schema(description="联系人姓名")
    private String name;

	/**
	* 手机号
	*/
    @Schema(description="手机号")
    private String phone;

	/**
	* 审核状态：默认 0：待审核，1：审核通过，2审核拒绝
	*/
    @Schema(description="审核状态：默认 0：待审核，1：审核通过，2审核拒绝")
    private Integer auditStatus;


}