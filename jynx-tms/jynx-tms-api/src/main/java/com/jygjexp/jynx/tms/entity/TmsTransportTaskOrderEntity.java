package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 运输任务单
 *
 * <AUTHOR>
 * @date 2025-04-07 15:31:29
 */
@Data
@TenantTable
@TableName("tms_transport_task_order")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "运输任务单")
public class TmsTransportTaskOrderEntity extends BaseLogicEntity<TmsTransportTaskOrderEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	 * 任务类型：1 揽收；2 派送
	 */
	@Schema(description="任务类型：1 揽收；2 派送")
	private Integer taskType;

	/**
	* 任务单号
	*/
    @Schema(description="任务单号")
    private String taskOrderNo;

	/**
	* 客户单号
	*/
    @Schema(description="客户单号")
    private String customerOrderNumber;

	/**
	* 跟踪单号
	*/
    @Schema(description="跟踪单号")
    private String entrustedOrderNumber;

	/**
	* 司机ID
	*/
    @Schema(description="司机ID")
    private Long driverId;

	/**
	 * 司机联系方式
	 */
	@Schema(description="司机联系方式")
	private String contactPhone;

	/**
	* 车牌号
	*/
    @Schema(description="车牌号")
    private String licensePlate;

	/**
	* 订单类型：1=托盘，2=包裹
	*/
    @Schema(description="订单类型：1=托盘，2=包裹")
    private Integer orderType;

	/**
	* 货物数量
	*/
    @Schema(description="货物数量")
    private Integer cargoQuantity;

	/**
	* 任务状态(25001=待提货、25002=配送中、25003=已完成)
	*/
    @Schema(description="任务状态(25001=待提货、25002=配送中、25003=已完成)")
    private Integer taskStatus;

	/**
	* 发货人姓名
	*/
    @Schema(description="发货人姓名")
    private String shipperName;

	/**
	* 发货人电话
	*/
    @Schema(description="发货人电话")
    private String shipperPhone;

	/**
	* 始发地
	*/
    @Schema(description="始发地")
    private String origin;

	/**
	* 发货邮编
	*/
    @Schema(description="发货邮编")
    private String shipperPostalCode;

	/**
	* 发货详细地址
	*/
    @Schema(description="发货详细地址")
    private String shipperAddress;

	/**
	* 预计发货时间开始
	*/
    @Schema(description="预计发货时间开始")
    private LocalDateTime estimatedShippingTimeStart;

	/**
	* 预计发货时间结束
	*/
    @Schema(description="预计发货时间结束")
    private LocalDateTime estimatedShippingTimeEnd;

	/**
	* 是否尾板提货：0 否，1 是
	*/
    @Schema(description="是否尾板提货：0 否，1 是")
    private Integer isTailgatePickup;

	/**
	* 到货人姓名
	*/
    @Schema(description="到货人姓名")
    private String receiverName;

	/**
	* 到货人电话
	*/
    @Schema(description="到货人电话")
    private String receiverPhone;

	/**
	* 目的地
	*/
    @Schema(description="目的地")
    private String destination;

	/**
	* 到货邮编
	*/
    @Schema(description="到货邮编")
    private String destPostalCode;

	/**
	* 到货详细地址
	*/
    @Schema(description="到货详细地址")
    private String destAddress;

	/**
	* 预计到货时间开始
	*/
    @Schema(description="预计到货时间开始")
    private LocalDateTime estimatedArrivalTimeStart;

	/**
	* 预计到货时间结束
	*/
    @Schema(description="预计到货时间结束")
    private LocalDateTime estimatedArrivalTimeEnd;

	/**
	* 是否尾板卸货：0 否，1 是
	*/
    @Schema(description="是否尾板卸货：0 否，1 是")
    private Integer isTailgateUnloaded;

	/**
	* 承运商ID
	*/
    @Schema(description="承运商ID")
    private Long carrierId;

	/**
	* 委托客户ID
	*/
    @Schema(description="委托客户ID")
    private Long customerId;

	/**
	* 仓库ID
	*/
	@NotNull
	@Schema(description="仓库ID")
	private Long siteId;

	/**
	* 运输类型：1=整车运输，2=零担运输
	*/
    @Schema(description="运输类型：1=整车运输，2=零担运输")
    private Integer transportType;

	/**
	* 货物类型：1=普通货物，2=危险货物
	*/
    @Schema(description="货物类型：1=普通货物，2=危险货物")
    private Integer cargoType;

	/**
	* 地址类型：1=住宅-不需要尾板，2=住宅-需要尾板，3=商业地址-需要尾板
	*/
    @Schema(description="地址类型：1=住宅-不需要尾板，2=住宅-需要尾板，3=商业地址-需要尾板")
    private Integer addressType;

	/**
	* 业务模式：1 揽收，2 中大件，3 卡派
	*/
    @Schema(description="业务模式：1 揽收，2 中大件，3 卡派")
    private Integer businessModel;

	/**
	* 总重量(kg)
	*/
    @Schema(description="总重量(kg)")
    private BigDecimal totalWeight;

	/**
	* 总体积(m³)
	*/
    @Schema(description="总体积(m³)")
    private BigDecimal totalVolume;

	/**
	* 是否删除
	*/
    @Schema(description="是否删除")
    private Integer isDelete;

	/**
	 * 发货地经纬度
	 */
	@Schema(description="发货地经纬度")
	private String shipperLatLng;

	/**
	 * 到货地经纬度
	 */
	@Schema(description="到货地经纬度")
	private String receiverLatLng;
	/**
	 * 订单数
	 */
	@Schema(description="订单数")
	private Integer orderCount;

	/**
	 * 规划名称-派送任务使用
	 */
	@Schema(description="规划名称-派送任务使用")
	private String planName;

}