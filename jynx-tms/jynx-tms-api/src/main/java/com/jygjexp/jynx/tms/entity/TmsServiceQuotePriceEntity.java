package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 服务商报价-价格配置子表
 *
 * <AUTHOR>
 * @date 2025-07-09 18:33:18
 */
@Data
@TenantTable
@TableName("tms_service_quote_price")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "服务商报价-价格配置子表")
public class TmsServiceQuotePriceEntity extends BaseLogicEntity<TmsServiceQuotePriceEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 报价主表ID
	*/
    @Schema(description="报价主表ID")
    private Long quoteId;

	/**
	* 邮编分区
	*/
    @Schema(description="邮编分区")
    private String regionName;

	/**
	* 起始重量(kg)
	*/
    @Schema(description="起始重量(kg)")
    private BigDecimal weightStart;

	/**
	* 结束重量(kg)
	*/
    @Schema(description="结束重量(kg)")
    private BigDecimal weightEnd;

	/**
	* 价格(CAD)
	*/
    @Schema(description="价格(CAD)")
    private BigDecimal price;

	/**
	* 会员等级
	*/
    @Schema(description="会员等级")
    private Integer memberLevel;

	/**
	* 利润率(%)
	*/
    @Schema(description="利润率(%)")
    private BigDecimal profitRate;

	/**
	* 启用状态：0：禁用、1：启用
	*/
    @Schema(description="启用状态：0：禁用、1：启用")
    private Integer isValid;

}